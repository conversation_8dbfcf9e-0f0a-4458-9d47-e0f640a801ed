import os
import sys
import sqlite3
from werkzeug.security import generate_password_hash

def get_db_connection():
    # 获取脚本所在目录的上级目录（项目根目录）
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    db_path = os.path.join(base_dir, 'forum.db')
    
    # 确保数据库文件存在
    if not os.path.exists(db_path):
        print('错误：数据库文件不存在')
        sys.exit(1)
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def validate_password(password):
    if len(password) < 6:
        return False, '密码长度必须至少为6个字符'
    return True, ''

def add_user(username, password):
    # 验证输入
    if not username or not password:
        print('错误：用户名和密码不能为空')
        return False
    
    # 验证密码
    is_valid, message = validate_password(password)
    if not is_valid:
        print(f'错误：{message}')
        return False
    
    # 生成密码哈希
    password_hash = generate_password_hash(password)
    
    conn = get_db_connection()
    try:
        # 检查用户名是否已存在
        existing_user = conn.execute('SELECT 1 FROM users WHERE username = ?', (username,)).fetchone()
        if existing_user:
            print('错误：用户名已存在')
            return False
        
        # 添加新用户
        conn.execute('INSERT INTO users (username, password_hash) VALUES (?, ?)',
                    (username, password_hash))
        conn.commit()
        print(f'成功：用户 {username} 已创建')
        return True
    except sqlite3.Error as e:
        print(f'数据库错误：{str(e)}')
        return False
    finally:
        conn.close()

def main():
    print('=== 添加新用户 ===')
    
    # 获取用户输入
    username = input('请输入用户名: ').strip()
    password = input('请输入密码: ').strip()
    
    # 添加用户
    add_user(username, password)

if __name__ == '__main__':
    main()
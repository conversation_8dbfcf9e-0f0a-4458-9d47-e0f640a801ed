<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的论坛 - {{ post.title }}</title>
    <link rel="icon" href="{{ url_for('static', filename='images/favicon.svg') }}" type="image/svg+xml">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <!-- Markdown样式 -->
    <style>
        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin: 1.5em 0 0.5em 0;
            font-weight: bold;
            line-height: 1.2;
        }
        .markdown-content h1 { font-size: 1.8em; color: #2563eb; }
        .markdown-content h2 { font-size: 1.5em; color: #3b82f6; }
        .markdown-content h3 { font-size: 1.3em; color: #6366f1; }
        .markdown-content h4 { font-size: 1.1em; color: #8b5cf6; }
        .markdown-content h5, .markdown-content h6 { font-size: 1em; color: #a855f7; }

        .markdown-content p { margin: 0.8em 0; line-height: 1.6; }
        .markdown-content ul, .markdown-content ol { margin: 0.4em 0; padding-left: 2em; }
        .markdown-content ul { list-style-type: disc; }
        .markdown-content ol { list-style-type: decimal; }
        .markdown-content li { margin: 0.1em 0; }

        /* 进一步优化段落间距 */
        .markdown-content p + p { margin-top: 0.8em; }
        .markdown-content p:first-child { margin-top: 0; }
        .markdown-content p:last-child { margin-bottom: 0; }

        .markdown-content blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1em 0;
            padding: 0.5em 1em;
            background-color: #f9fafb;
            font-style: italic;
        }

        .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .markdown-content pre {
            background-color: #1f2937;
            color: #f9fafb;
            padding: 1em;
            border-radius: 6px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
            color: inherit;
        }

        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #d1d5db;
            padding: 0.5em;
            text-align: left;
        }

        .markdown-content th {
            background-color: #f3f4f6;
            font-weight: bold;
        }

        .markdown-content a {
            color: #2563eb;
            text-decoration: underline;
        }

        .markdown-content a:hover {
            color: #1d4ed8;
        }

        .markdown-content hr {
            border: none;
            border-top: 2px solid #e5e7eb;
            margin: 2em 0;
        }

        .markdown-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 0.5em 0;
        }

        .markdown-content .task-list-item {
            list-style: none;
        }

        .markdown-content .task-list-item input[type="checkbox"] {
            margin-right: 0.5em;
        }

        /* 回复框标题和字数统计样式 */
        .reply-form .flex.items-center h4 {
            margin: 0;
            line-height: 1.5;
        }
        
        .reply-form .flex.items-center .selection-count,
        .reply-form .flex.items-center .character-count {
            font-size: 0.75rem;
            color: #6b7280;
            line-height: 1.5;
        }
        
        .reply-form .flex.items-center .selection-count.highlight {
            color: #2563eb;
            font-weight: 500;
        }

        /* 侧边栏适配的回复框样式 */
        @media (min-width: 769px) {
            .fixed-reply-container {
                left: 250px; /* 桌面端考虑侧边栏宽度 */
            }
        }

        /* 悬浮回复按钮样式 */
        .floating-reply-btn {
            position: fixed;
            bottom: 24px;
            right: 24px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            z-index: 40;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .floating-reply-btn:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .floating-reply-btn {
                bottom: 16px;
                right: 16px;
                width: 48px;
                height: 48px;
            }
        }
    </style>
    <script>
        // 回复相关函数

        // 草稿存储相关功能
        function getDraftKey(postId, replyId = 'new') {
            return `haobbs_draft_post_${postId}_reply_${replyId}`;
        }

        function saveDraft(postId, replyId, content) {
            if (!content.trim()) {
                // 空内容不保存
                removeDraft(postId, replyId);
                return;
            }

            const draftData = {
                content: content,
                timestamp: Date.now(),
                expires: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7天后过期
            };

            localStorage.setItem(getDraftKey(postId, replyId), JSON.stringify(draftData));
        }

        function loadDraft(postId, replyId) {
            const key = getDraftKey(postId, replyId);
            const draftJson = localStorage.getItem(key);
            
            if (!draftJson) return null;

            try {
                const draftData = JSON.parse(draftJson);
                
                // 检查是否过期
                if (Date.now() > draftData.expires) {
                    localStorage.removeItem(key);
                    return null;
                }

                return draftData.content;
            } catch (e) {
                console.error('Failed to parse draft data:', e);
                localStorage.removeItem(key);
                return null;
            }
        }

        function removeDraft(postId, replyId) {
            localStorage.removeItem(getDraftKey(postId, replyId));
        }

        // 清理所有过期草稿
        function cleanupExpiredDrafts() {
            const now = Date.now();
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('haobbs_draft_post_')) {
                    try {
                        const draftData = JSON.parse(localStorage.getItem(key));
                        if (now > draftData.expires) {
                            localStorage.removeItem(key);
                        }
                    } catch (e) {
                        // 无效数据，直接删除
                        localStorage.removeItem(key);
                    }
                }
            }
        }

        function handleDeleteReply(event, replyId) {
            if (!confirm('确定要删除这个回复吗？')) return false;

            fetch('/reply/' + replyId + '/delete', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
            .then(response => {
                if (response.ok) {
                    document.querySelector('[data-reply-id="' + replyId + '"]').remove();
                } else {
                    alert('删除失败');
                }
            });
            return false;
        }

        function handleDeletePost(event, postId) {
            event.preventDefault();
            if (!confirm('确定要删除这个帖子吗？')) return;

            fetch('/post/' + postId + '/delete', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/';
                    } else {
                        alert(data.message);
                    }
                });
        }

        function toggleSortOrder() {
            const currentSort = "{{ current_sort }}";
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
            window.location.href = `{{ url_for('view_post', post_id=post.id) }}?page={{ page }}&sort=${newSort}`;
        }


    </script>
</head>

<body class="font-sans m-0 p-0 leading-relaxed">
    {% include 'components/sidebar.html' %}
    <div class="main-content" id="mainContent">
        <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">
        <!-- 回复列表标题和排序控制 -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center my-6 py-2 border-b-2 border-gray-100 gap-4">
            <h3 class="m-0 text-lg text-gray-800 font-semibold">{{ post.title }}（{{ total_replies }}条）</h3>
            <div class="flex items-center gap-4 text-sm w-full md:w-auto">
                <button class="bg-gray-100 border border-gray-300 text-gray-700 px-4 py-2 rounded-full cursor-pointer transition-all duration-200 text-sm font-medium flex items-center gap-1 hover:bg-gray-200 hover:border-gray-400 hover:text-gray-900 w-full md:w-auto justify-center" onclick="toggleSortOrder()">
                    {{ '时间倒序 ▼' if current_sort == 'desc' else '时间正序 ▲' }}
                </button>
            </div>
        </div>

        <!-- 帖子内容 -->
        <div class="card mb-4 border border-gray-200">
            <div class="text-gray-500 text-sm mb-2 flex justify-between">
                <span>{{ post.created_at | beijing_time }}</span>
                <div class="flex gap-2">
                    <a href="{{ url_for('edit_post', post_id=post.id) }}" class="btn-edit text-xs px-3 py-1">编辑</a>
                    <!-- 只在无回复时显示删除按钮 -->
                    {% if replies|length == 0 %}
                    <form onsubmit="handleDeletePost(event, '{{ post.id }}')">
                        <button type="submit" class="btn-delete text-xs px-3 py-1">删除</button>
                    </form>
                    {% endif %}
                </div>
            </div>
            <div class="text-gray-800 font-sans my-4 break-words markdown-content" id="post-content" style="font-size: 15px;">{{ post.content|markdown|safe }}</div>
            <div class="text-gray-500 text-sm" style="text-align: right;">
                字数统计：{{ post.content|word_count }} 字
            </div>
        </div>


        <!-- 悬浮回复按钮 -->
        <button id="floating-reply-btn" class="floating-reply-btn" title="发表回复">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
        </button>

        <!-- 底部固定回复框 -->
        <div class="fixed-reply-container hidden" id="reply-container">
            <div class="fixed-reply-box">
                <form id="reply-form" method="post" class="reply-form">
                    <!-- 标题栏 -->
                    <div class="flex justify-between items-center mb-3 px-1">
                        <!-- 左侧：关闭按钮 -->
                        <button type="button" id="close-reply-btn" class="close-btn flex items-center justify-center w-8 h-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors duration-200" title="关闭回复框">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                <line x1="6" y1="6" x2="18" y2="18"></line>
                            </svg>
                        </button>
                        
                        <!-- 中间：标题 -->
                        <h4 id="reply-title" class="text-base font-semibold text-gray-800 m-0">发表回复</h4>
                        
                        <!-- 右侧：提交按钮 -->
                        <button type="submit" class="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors duration-200" title="提交回复">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="22" y1="2" x2="11" y2="13"></line>
                                <polygon points="22,2 15,22 11,13 2,9"></polygon>
                            </svg>
                        </button>
                    </div>
                    <div class="relative" style="position: relative;">
                        <textarea name="content" id="reply-content" placeholder="支持Markdown语法，如：**粗体** *斜体* 等..." required class="w-full text-sm leading-relaxed px-4 py-3 border border-gray-300 rounded-lg resize-y focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200" style="min-height: 120px; padding-bottom: 40px;"></textarea>
                        <div class="absolute bottom-2 right-2 flex items-center gap-2" style="position: absolute; bottom: 0.5rem; right: 0.5rem; z-index: 10;">
                            <span id="reply-selection-count" class="selection-count empty text-xs text-gray-500 bg-white px-1 rounded">选中: 0 字</span>
                            <span id="reply-character-count" class="character-count text-xs text-gray-500 bg-white px-1 rounded">0 字</span>
                            <button type="button" id="expand-toggle" class="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200 bg-white rounded border border-gray-300" title="展开/收起">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="18 15 12 9 6 15"></polyline>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <!-- 隐藏字段用于存储编辑的回复ID -->
                    <input type="hidden" id="edit-reply-id" name="edit_reply_id" value="">
                </form>
            </div>
        </div>

        <div id="replies-list">
            {% for reply in replies %}
            <div class="card mb-4 border border-gray-200" data-reply-id="{{ reply.id }}">
                <div class="text-gray-500 text-sm mb-2 flex justify-between">
                    <span>{{ reply.created_at | beijing_time }}</span>
                    <div class="flex gap-2">
                        {% set edit_url = url_for('edit_reply', reply_id=reply.id) %}
                        <button class="btn-edit text-xs px-3 py-1" data-reply-id="{{ reply.id }}">编辑</button>
                        <form onsubmit="return handleDeleteReply(event, '{{ reply.id }}');">
                            <button type="submit" class="btn-delete text-xs px-3 py-1">删除</button>
                        </form>
                    </div>
                </div>
                <div class="text-gray-800 my-2 break-words font-sans tracking-wide py-1 text-justify markdown-content" id="reply-{{ reply.id }}" style="font-size: 15px;">{{ reply.content|markdown|safe }}</div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            {% if total_pages > 1 %}
            {% for p in range(1, total_pages + 1) %}
            <a href="{{ url_for('view_post', post_id=post.id, page=p) }}"
                class="page-link {% if p == page %}active{% endif %}">{{ p }}</a>
            {% endfor %}
            {% endif %}
        </div>
    </div>
    </div>



    <script>
        // 展开/收起功能
        let isExpanded = false;

        function toggleTextareaHeight() {
            const textarea = document.getElementById('reply-content');
            const toggleButton = document.getElementById('expand-toggle');
            const svg = toggleButton.querySelector('svg');
            
            if (isExpanded) {
                // 收起
                textarea.style.height = '120px';
                svg.innerHTML = '<polyline points="18 15 12 9 6 15"></polyline>';
                toggleButton.title = "展开";
            } else {
                // 展开
                textarea.style.height = '360px';
                svg.innerHTML = '<polyline points="6 9 12 15 18 9"></polyline>';
                toggleButton.title = "收起";
            }
            
            isExpanded = !isExpanded;
        }

        // 字符统计函数（只统计中文、英文和数字）
        function countAllCharacters(text) {
            // 使用正则表达式匹配中文、英文和数字字符
            const validChars = text.match(/[\u4e00-\u9fa5a-zA-Z0-9]/g);
            return validChars ? validChars.length : 0;
        }

        // 更新字数统计
        function updateCharacterCount(textarea, charCount) {
            const text = textarea.value;
            const count = countAllCharacters(text);
            charCount.textContent = count + ' 字';
        }

        // 更新选中统计
        function updateSelectionCount(textarea, selectionCount) {
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            if (start === end) {
                // 没有选中文本
                selectionCount.textContent = '选中: 0 字';
                selectionCount.classList.add('empty');
                selectionCount.classList.remove('highlight');
            } else {
                // 有选中文本，统计其中的字符
                const selectedText = textarea.value.substring(start, end);
                const count = countAllCharacters(selectedText);
                selectionCount.textContent = '选中: ' + count + ' 字';
                selectionCount.classList.remove('empty');
                
                // 如果选中文本包含字符，高亮显示
                if (count > 0) {
                    selectionCount.classList.add('highlight');
                } else {
                    selectionCount.classList.remove('highlight');
                }
            }
        }

        // 初始化回复框字数统计
        function initReplyCharacterCount() {
            const replyTextarea = document.getElementById('reply-content');
            const replyCharCount = document.getElementById('reply-character-count');
            const replySelectionCount = document.getElementById('reply-selection-count');

            if (replyTextarea && replyCharCount && replySelectionCount) {
                // 初始化字数统计
                updateCharacterCount(replyTextarea, replyCharCount);
                updateSelectionCount(replyTextarea, replySelectionCount);

                // 监听输入事件实时更新字数
                replyTextarea.addEventListener('input', () => {
                    updateCharacterCount(replyTextarea, replyCharCount);
                    updateSelectionCount(replyTextarea, replySelectionCount);
                    
                    // 自动保存草稿
                    const replyId = document.getElementById('edit-reply-id').value || 'new';
                    saveDraft('{{ post.id }}', replyId, replyTextarea.value);
                });
                
                replyTextarea.addEventListener('propertychange', () => updateCharacterCount(replyTextarea, replyCharCount));

                // 监听选中相关事件
                replyTextarea.addEventListener('select', () => updateSelectionCount(replyTextarea, replySelectionCount));
                replyTextarea.addEventListener('selectionchange', () => updateSelectionCount(replyTextarea, replySelectionCount));
                replyTextarea.addEventListener('mouseup', () => updateSelectionCount(replyTextarea, replySelectionCount));
                replyTextarea.addEventListener('keyup', () => updateSelectionCount(replyTextarea, replySelectionCount));
            }
        }

        
        // 悬浮按钮控制函数
        function toggleReplyContainer() {
            const replyContainer = document.getElementById('reply-container');
            const floatingBtn = document.getElementById('floating-reply-btn');
            
            if (replyContainer.classList.contains('hidden')) {
                replyContainer.classList.remove('hidden');
                floatingBtn.style.transform = 'rotate(45deg)';
                floatingBtn.title = "收起回复框";
            } else {
                replyContainer.classList.add('hidden');
                floatingBtn.style.transform = 'rotate(0deg)';
                floatingBtn.title = "发表回复";
            }
        }

        // 关闭回复框函数
        function closeReplyContainer() {
            const replyContainer = document.getElementById('reply-container');
            const floatingBtn = document.getElementById('floating-reply-btn');
            
            replyContainer.classList.add('hidden');
            floatingBtn.style.transform = 'rotate(0deg)';
            floatingBtn.title = "发表回复";
        }

        // 页面加载时初始化回复框字数统计和展开/收起功能
        document.addEventListener('DOMContentLoaded', function() {
            initReplyCharacterCount();
            
            // 绑定展开/收起按钮点击事件
            const expandToggle = document.getElementById('expand-toggle');
            if (expandToggle) {
                expandToggle.addEventListener('click', toggleTextareaHeight);
            }
            
            // 绑定悬浮按钮点击事件
            const floatingBtn = document.getElementById('floating-reply-btn');
            if (floatingBtn) {
                floatingBtn.addEventListener('click', toggleReplyContainer);
            }

            // 绑定关闭按钮点击事件
            const closeBtn = document.getElementById('close-reply-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', closeReplyContainer);
            }


            // 绑定表单提交处理
            const replyForm = document.getElementById('reply-form');
            if (replyForm) {
                replyForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handleReplySubmit(e);
                });
            }

            // 清理过期草稿
            cleanupExpiredDrafts();

            // 页面加载时自动恢复新回复的草稿
            const newReplyDraft = loadDraft('{{ post.id }}', 'new');
            if (newReplyDraft) {
                document.getElementById('reply-content').value = newReplyDraft;
                // 更新字数统计
                const replyTextarea = document.getElementById('reply-content');
                const replyCharCount = document.getElementById('reply-character-count');
                const replySelectionCount = document.getElementById('reply-selection-count');
                updateCharacterCount(replyTextarea, replyCharCount);
                updateSelectionCount(replyTextarea, replySelectionCount);
            }
        });
        
        document.addEventListener('click', function(event) {
            if (event.target.matches('.btn-edit') && event.target.dataset.replyId) {
                event.preventDefault();
                const replyId = event.target.dataset.replyId;
                startEditReply(replyId);
            }
        });


        let currentEditReplyId = null;

        // 开始编辑回复
        async function startEditReply(replyId) {
            try {
                let content = '';
                
                // 首先尝试从本地存储加载草稿
                const draftContent = loadDraft('{{ post.id }}', replyId);
                if (draftContent) {
                    content = draftContent;
                } else {
                    // 如果没有草稿，从服务器获取原始内容
                    const response = await fetch(`/reply/${replyId}/edit`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        content = data.content;
                    } else {
                        // 如果获取失败，使用页面上的文本内容作为备选
                        const replyContentElement = document.getElementById(`reply-${replyId}`);
                        content = replyContentElement.textContent;
                    }
                }

                // 填充回复框
                document.getElementById('reply-content').value = content;
                document.getElementById('edit-reply-id').value = replyId;
                currentEditReplyId = replyId;

                // 更新标题为编辑模式
                document.getElementById('reply-title').textContent = '编辑回复';
                
                // 打开回复框
                toggleReplyContainer();
                
                // 聚焦到文本区域
                setTimeout(() => {
                    document.getElementById('reply-content').focus();
                }, 100);

            } catch (error) {
                console.error('获取原始内容失败:', error);
                alert('获取回复内容失败，请重试');
            }
        }

        // 取消编辑
        function cancelEditReply() {
            document.getElementById('reply-content').value = '';
            document.getElementById('edit-reply-id').value = '';
            currentEditReplyId = null;
            
            // 恢复标题为发表回复模式
            document.getElementById('reply-title').textContent = '发表回复';
            
            // 关闭回复框
            closeReplyContainer();
        }

        // 处理回复提交（新建和编辑）
        async function handleReplySubmit(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            const replyId = document.getElementById('edit-reply-id').value;
            const isEditMode = replyId !== '';

            try {
                let response;
                if (isEditMode) {
                    // 编辑现有回复
                    response = await fetch(`/reply/${replyId}/edit`, {
                        method: 'POST',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    });
                } else {
                    // 新建回复
                    response = await fetch(`/reply/{{ post.id }}`, {
                        method: 'POST',
                        body: formData
                    });
                }

                if (response.ok) {
                    const data = await response.json();
                    
                    // 提交成功后删除对应的草稿
                    removeDraft('{{ post.id }}', isEditMode ? replyId : 'new');
                    
                    if (isEditMode) {
                        // 更新页面中的回复内容
                        const replyElement = document.querySelector(`[data-reply-id="${data.id}"]`);
                        if (replyElement) {
                            const contentElement = replyElement.querySelector(`#reply-${data.id}`);
                            if (contentElement) {
                                contentElement.innerHTML = data.content;
                            }
                        }
                        // 重置编辑状态
                        cancelEditReply();
                    } else {
                        // 新建回复成功后刷新页面
                        window.location.reload();
                    }
                } else {
                    throw new Error('提交失败');
                }
            } catch (error) {
                alert(error.message || '提交失败，请重试');
            }
        }

    </script>
</body>

</html>

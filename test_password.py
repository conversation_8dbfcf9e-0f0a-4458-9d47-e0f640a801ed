import sqlite3
from werkzeug.security import check_password_hash

# 连接到本地数据库
conn = sqlite3.connect('forum.db')
conn.row_factory = sqlite3.Row

# 获取admin用户信息
user = conn.execute('SELECT * FROM users WHERE username = ?', ('admin',)).fetchone()
conn.close()

if user:
    print(f"用户名: {user['username']}")
    print(f"密码哈希: {user['password_hash']}")
    
    # 测试密码
    test_password = 'admin2025'
    if check_password_hash(user['password_hash'], test_password):
        print("密码验证成功!")
    else:
        print("密码验证失败!")
else:
    print("用户不存在")

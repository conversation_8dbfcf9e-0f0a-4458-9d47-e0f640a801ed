from werkzeug.security import check_password_hash

# 服务器上admin用户的密码哈希
password_hash = "scrypt:32768:8:1$OSK9fgdZLnBQrBJp$56d036fafa9df486e99302caeb9215ba0999aafe90f39a8573458edddd727f20165c1d82d7df53aeec7fb03b06e5f91f6adae4b7ab30ab9d680136f800f0d9af"

# 测试密码
test_password = "admin2025"

if check_password_hash(password_hash, test_password):
    print("密码验证成功！")
    print("用户名: admin")
    print("密码: admin2025")
else:
    print("密码验证失败！")
